import time

from models.models import Product, ProductTrafficMetrics, AccountTrafficMetrics
from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from omni.scheduler.base_schedule import BaseScheduler
from omni.scheduler.schedule_register import register_scheduler


async def execute_task() -> None:
    """
    处理广告主产品流量统计
    通过 Product -> AccountTrafficMetrics -> ProductTrafficMetrics 三个表的关系进行统计
    """
    # 查询所有未删除的产品
    products = await Product.find(Product.is_deleted == False).to_list()
    olog.info(f"发现 {len(products)} 个产品需要处理。")

    if not products:
        olog.info("没有产品需要处理。")
        return

    # 获取所有产品ID
    product_ids = [str(product.id) for product in products]

    # 批量查询所有相关的账号流量指标，避免N+1查询
    all_account_metrics = await AccountTrafficMetrics.find(
        AccountTrafficMetrics.product_id.in_(product_ids)
    ).to_list()

    # 按产品ID分组账号流量指标
    metrics_by_product = {}
    for metric in all_account_metrics:
        product_id = metric.product_id
        if product_id not in metrics_by_product:
            metrics_by_product[product_id] = []
        metrics_by_product[product_id].append(metric)

    olog.info(f"查询到 {len(all_account_metrics)} 条账号流量指标数据。")

    processed_count = 0

    for product in products:
        product_id_str = str(product.id)
        user_id_str = product.user_id

        # 获取该产品的流量指标数据
        account_metrics = metrics_by_product.get(product_id_str, [])

        # 如果没有流量数据，跳过该产品
        if not account_metrics:
            continue

        # 计算各项统计指标
        total_view_count = 0
        total_like_count = 0
        total_comment_count = 0
        total_favorite_count = 0
        total_share_count = 0
        platforms = set()

        for metric in account_metrics:
            # 累加各项指标
            total_view_count += metric.view_count or 0
            total_like_count += metric.like_count or 0
            total_comment_count += metric.comment_count or 0
            total_favorite_count += metric.favorite_count or 0
            total_share_count += metric.share_count or 0

            # 收集平台信息
            if metric.platform:
                platforms.add(metric.platform)

        # 如果所有指标都为0，跳过该产品
        if (total_view_count == 0 and total_like_count == 0 and
            total_comment_count == 0 and total_favorite_count == 0 and
            total_share_count == 0):
            continue

        # 准备更新数据
        traffic_data = {
            "platforms": list(platforms),
            "total_view_count": total_view_count,
            "total_like_count": total_like_count,
            "total_comment_count": total_comment_count,
            "total_favorite_count": total_favorite_count,
            "total_share_count": total_share_count,
            "last_updated_at": int(time.time())
        }

        # 更新或插入产品流量统计数据
        await ProductTrafficMetrics.find_one(
            (ProductTrafficMetrics.user_id == user_id_str) &
            (ProductTrafficMetrics.product_id == product_id_str)
        ).upsert(
            {"$set": traffic_data},
            on_insert={"user_id": user_id_str, "product_id": product_id_str}
        )

        processed_count += 1
        olog.info(f"产品 {product_id_str} 的流量统计数据已更新。")

    olog.info(f"共处理了 {processed_count} 个产品的流量统计数据。")


@register_scheduler(trigger="cron", hour="*/2", minute="0")
class ProductTrafficMetricsScheduler(BaseScheduler):
    async def run_task(self) -> None:
        """
        执行广告主产品流量统计任务
        
        调度频率：每2小时执行一次
        """
        olog.info("开始执行广告主流量统计任务...")
        await execute_task()
        olog.info("广告主流量统计任务执行完毕.")


if __name__ == "__main__":
    import asyncio
    
    async def main():
        await init_models()
        await execute_task()
    
    asyncio.run(main())
